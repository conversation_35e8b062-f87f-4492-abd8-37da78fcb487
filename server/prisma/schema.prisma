generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model School {
  id        String   @id @default(cuid())
  name      String
  slug      String   @unique
  users     User[]
  students  Student[]
  teachers  Teacher[]

  classes   Class[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model User {
  id        String  @id @default(cuid())
  email     String  @unique
  password  String
  name      String
  role      Role
  school    School? @relation(fields: [schoolId], references: [id])
  schoolId  String?
  student   Student?
  teacher   Teacher?
  sentMessages     Message[] @relation("SentMessages")
  receivedMessages Message[] @relation("ReceivedMessages")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

enum Role {
  admin
  teacher
  student
}

model Student {
  id             String   @id @default(cuid())
  user           User?    @relation(fields: [userId], references: [id])
  userId         String?  @unique
  school         School?  @relation(fields: [schoolId], references: [id])
  schoolId       String?
  firstName      String
  lastName       String
  email          String   @unique
  grade          String
  dateOfBirth    DateTime
  enrollmentDate DateTime
  status         StudentStatus @default(active)
  parentName     String
  parentEmail    String
  parentPhone    String
  address        String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  deletedAt      DateTime?
  enrollments    Enrollment[]
  attendances    Attendance[]
  grades         Grade[]
  submissions    Submission[]
  examSubmissions ExamSubmission[]
}

enum StudentStatus {
  active
  inactive
  graduated
}

model Teacher {
  id               String   @id @default(cuid())
  user             User     @relation(fields: [userId], references: [id])
  userId           String   @unique
  school           School?  @relation(fields: [schoolId], references: [id])
  schoolId         String?
  firstName        String
  lastName         String
  email            String   @unique
  phone            String
  department       String
  subject          String
  hireDate         DateTime
  status           TeacherStatus @default(active)
  qualification    String
  experience       Int
  address          String
  emergencyContact String
  emergencyPhone   String
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt
  deletedAt        DateTime?
  classes          Class[]
  attendanceRecorded Attendance[] @relation("TeacherRecordedAttendance")
  assignments        Assignment[]
  exams              Exam[]
}

enum TeacherStatus {
  active
  inactive
  on_leave
}

model Class {
  id               String   @id @default(cuid())
  school           School?  @relation(fields: [schoolId], references: [id])
  schoolId         String?
  name             String
  subject          String
  grade            String
  teacher          Teacher  @relation(fields: [teacherId], references: [id])
  teacherId        String
  teacherName      String
  room             String
  schedule         String
  startDate        DateTime
  endDate          DateTime
  description      String
  capacity         Int
  status           ClassStatus @default(pending)
  enrolledStudents Enrollment[]
  attendances      Attendance[]
  assignments      Assignment[]
  exams            Exam[]
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt
  deletedAt        DateTime?
}

enum ClassStatus {
  pending
  approved
  rejected
  active
  inactive
}

model Enrollment {
  id        String  @id @default(cuid())
  student   Student @relation(fields: [studentId], references: [id])
  studentId String
  class     Class   @relation(fields: [classId], references: [id])
  classId   String
  createdAt DateTime @default(now())
}

// Track attendance per class per date
model Attendance {
  id         String   @id @default(cuid())
  class      Class    @relation(fields: [classId], references: [id])
  classId    String
  student    Student  @relation(fields: [studentId], references: [id])
  studentId  String
  date       DateTime
  status     AttendanceStatus
  note       String?
  recordedBy Teacher? @relation("TeacherRecordedAttendance", fields: [recordedById], references: [id])
  recordedById String?
  createdAt  DateTime @default(now())

  @@unique([classId, studentId, date])
}

enum AttendanceStatus {
  present
  absent
  late
  excused
}

// Assignments for classes
model Assignment {
  id          String       @id @default(cuid())
  class       Class        @relation(fields: [classId], references: [id])
  classId     String
  teacher     Teacher?     @relation(fields: [teacherId], references: [id])
  teacherId   String?
  title       String
  description String
  dueDate     DateTime
  maxPoints   Int
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  deletedAt   DateTime?
  grades      Grade[]
  submissions Submission[]
}

// Student submissions for assignments
model Submission {
  id           String     @id @default(cuid())
  assignment   Assignment @relation(fields: [assignmentId], references: [id])
  assignmentId String
  student      Student    @relation(fields: [studentId], references: [id])
  studentId    String
  content      String
  attachments  Json?      // Array of file URLs/paths
  submittedAt  DateTime   @default(now())
  grade        Float?
  feedback     String?
  gradedAt     DateTime?

  @@unique([assignmentId, studentId])
}

// Messages between users
model Message {
  id          String    @id @default(cuid())
  sender      User      @relation("SentMessages", fields: [senderId], references: [id])
  senderId    String
  recipient   User      @relation("ReceivedMessages", fields: [recipientId], references: [id])
  recipientId String
  subject     String
  content     String
  isRead      Boolean   @default(false)
  isStarred   Boolean   @default(false)
  isArchived  Boolean   @default(false)
  flaggedToAdmin Boolean @default(false)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  deletedAt   DateTime?
}

// Grades per assignment per student
model Grade {
  id           String     @id @default(cuid())
  assignment   Assignment @relation(fields: [assignmentId], references: [id])
  assignmentId String
  student      Student    @relation(fields: [studentId], references: [id])
  studentId    String
  score        Float
  feedback     String?
  gradedAt     DateTime @default(now())

  @@unique([assignmentId, studentId])
}

// Exam System Models
model Exam {
  id          String     @id @default(cuid())
  title       String
  description String?
  class       Class      @relation(fields: [classId], references: [id])
  classId     String
  teacher     Teacher    @relation(fields: [teacherId], references: [id])
  teacherId   String

  // Exam Configuration
  examType    ExamType   @default(MIXED)
  duration    Int        // Duration in minutes
  totalMarks  Int        @default(0)
  passingMarks Int       @default(0)

  // Scheduling
  startDate   DateTime
  endDate     DateTime
  isActive    Boolean    @default(true)

  // Questions and Submissions
  questions   Question[]
  submissions ExamSubmission[]

  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
}

enum ExamType {
  MCQ         // Multiple Choice Questions only
  STRUCTURAL  // Essay/Structural questions only
  MIXED       // Both MCQ and Structural
}

model Question {
  id          String       @id @default(cuid())
  exam        Exam         @relation(fields: [examId], references: [id], onDelete: Cascade)
  examId      String

  // Question Details
  questionText String
  questionType QuestionType
  marks       Int          @default(1)
  order       Int          // Order of question in exam

  // MCQ Specific Fields
  optionA     String?
  optionB     String?
  optionC     String?
  optionD     String?
  correctAnswer String?    // For MCQ: A, B, C, or D

  // Structural Question Fields
  maxWords    Int?         // Maximum word limit for structural questions

  // Student Answers
  answers     StudentAnswer[]

  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
}

enum QuestionType {
  MCQ
  STRUCTURAL
}

model ExamSubmission {
  id          String    @id @default(cuid())
  exam        Exam      @relation(fields: [examId], references: [id])
  examId      String
  student     Student   @relation(fields: [studentId], references: [id])
  studentId   String

  // Submission Details
  startedAt   DateTime  @default(now())
  submittedAt DateTime?
  isSubmitted Boolean   @default(false)

  // Scoring
  totalScore  Int       @default(0)
  mcqScore    Int       @default(0)
  structuralScore Int   @default(0)
  isGraded    Boolean   @default(false)

  // Student Answers
  answers     StudentAnswer[]

  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@unique([examId, studentId])
}

model StudentAnswer {
  id           String         @id @default(cuid())
  question     Question       @relation(fields: [questionId], references: [id])
  questionId   String
  submission   ExamSubmission @relation(fields: [submissionId], references: [id])
  submissionId String

  // Answer Content
  mcqAnswer    String?        // A, B, C, or D for MCQ
  textAnswer   String?        // Text answer for structural questions

  // Grading
  isCorrect    Boolean?       // For MCQ - auto-graded
  marksAwarded Int           @default(0)
  feedback     String?        // Teacher feedback for structural questions

  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt

  @@unique([questionId, submissionId])
}

// Lightweight blockchain for audit
model AuditBlock {
  id         String   @id @default(cuid())
  index      Int
  prevHash   String
  data       Json
  timestamp  DateTime @default(now())
  hash       String
}

