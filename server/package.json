{"name": "school-management-server", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc -p tsconfig.json", "start": "node dist/index.js", "test": "vitest run --config vitest.config.ts --reporter=default", "test:watch": "vitest --config vitest.config.ts", "db:seed": "tsx prisma/seed.ts"}, "dependencies": {"@prisma/client": "^5.20.0", "argon2": "^0.41.1", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "jose": "^5.9.6", "node-fetch": "^3.3.2", "pino": "^9.3.2", "zod": "^3.23.8"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^4.17.21", "@types/node": "^22.10.1", "@types/supertest": "^6.0.3", "prisma": "^5.20.0", "supertest": "^7.0.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsx": "^4.19.2", "typescript": "^5.6.3", "vitest": "^2.1.4"}}