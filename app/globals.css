@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  /* Updated color tokens to match educational platform design brief with emerald green primary */
  --background: oklch(1 0 0); /* #ffffff */
  --foreground: oklch(0.556 0 0); /* #475569 */
  --card: oklch(0.97 0 0); /* #f1f5f9 */
  --card-foreground: oklch(0.445 0 0); /* #374151 */
  --popover: oklch(1 0 0); /* #ffffff */
  --popover-foreground: oklch(0.556 0 0); /* #475569 */
  --primary: oklch(0.646 0.222 164.376); /* #059669 emerald-600 */
  --primary-foreground: oklch(1 0 0); /* #ffffff */
  --secondary: oklch(0.97 0 0); /* #f1f5f9 */
  --secondary-foreground: oklch(0.445 0 0); /* #374151 */
  --muted: oklch(0.97 0 0); /* #f1f5f9 */
  --muted-foreground: oklch(0.556 0 0); /* #6b7280 */
  --accent: oklch(0.646 0.222 154.376); /* #10b981 emerald-500 */
  --accent-foreground: oklch(1 0 0); /* #ffffff */
  --destructive: oklch(0.577 0.245 27.325); /* #dc2626 */
  --destructive-foreground: oklch(1 0 0); /* #ffffff */
  --border: oklch(0.922 0 0); /* #d1d5db */
  --input: oklch(0.97 0 0); /* #f1f5f9 */
  --ring: oklch(0.646 0.222 164.376 / 0.5); /* rgba(5, 150, 105, 0.5) */
  --chart-1: oklch(0.646 0.222 164.376); /* #059669 */
  --chart-2: oklch(0.646 0.222 154.376); /* #10b981 */
  --chart-3: oklch(0.646 0.222 144.376); /* #34d399 */
  --chart-4: oklch(0.646 0.222 134.376); /* #6ee7b7 */
  --chart-5: oklch(0.646 0.222 124.376); /* #a7f3d0 */
  --radius: 0.5rem;
  --sidebar: oklch(1 0 0); /* #ffffff */
  --sidebar-foreground: oklch(0.445 0 0); /* #374151 */
  --sidebar-primary: oklch(0.646 0.222 164.376); /* #059669 */
  --sidebar-primary-foreground: oklch(1 0 0); /* #ffffff */
  --sidebar-accent: oklch(0.646 0.222 154.376); /* #10b981 */
  --sidebar-accent-foreground: oklch(1 0 0); /* #ffffff */
  --sidebar-border: oklch(0.922 0 0); /* #d1d5db */
  --sidebar-ring: oklch(0.646 0.222 164.376 / 0.5); /* rgba(5, 150, 105, 0.5) */
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.646 0.222 164.376);
  --primary-foreground: oklch(0.145 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.646 0.222 154.376);
  --accent-foreground: oklch(0.145 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.646 0.222 164.376 / 0.5);
  --chart-1: oklch(0.646 0.222 164.376);
  --chart-2: oklch(0.646 0.222 154.376);
  --chart-3: oklch(0.646 0.222 144.376);
  --chart-4: oklch(0.646 0.222 134.376);
  --chart-5: oklch(0.646 0.222 124.376);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.646 0.222 164.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.646 0.222 154.376);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.646 0.222 164.376 / 0.5);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
